import argparse
import numpy as np
import torch
import torch.nn.functional as F
import h5py
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from skimage.segmentation import slic, mark_boundaries
from skimage.measure import label, regionprops
from skimage.morphology import binary_dilation, disk
import cv2

from networks.net_factory import BCP_net

def compute_entropy(predictions):
    """计算预测的熵值作为不确定性度量"""
    epsilon = 1e-8
    predictions = predictions + epsilon
    entropy = -torch.sum(predictions * torch.log(predictions), dim=1)
    return entropy

def monte_carlo_dropout(model, image, num_samples=20):
    """使用Monte Carlo Dropout估计不确定性"""
    model.train()  # 启用dropout
    predictions = []
    
    with torch.no_grad():
        for _ in range(num_samples):
            pred = model(image)
            pred = torch.softmax(pred, dim=1)
            predictions.append(pred.cpu())
    
    predictions = torch.stack(predictions)
    mean_prediction = torch.mean(predictions, dim=0)
    entropy_uncertainty = compute_entropy(mean_prediction)
    
    return mean_prediction, entropy_uncertainty

def get_slic_superpixels(image, n_segments=30, compactness=0.1):
    """对图像进行SLIC超像素分割"""
    # 确保图像是2D的
    if len(image.shape) == 3:
        image = image.squeeze()
    
    # 归一化到0-1范围
    image_norm = (image - image.min()) / (image.max() - image.min())
    
    # 进行SLIC分割
    segments = slic(image_norm, n_segments=n_segments, compactness=compactness, 
                   start_label=1, channel_axis=None)
    
    return segments

def select_high_uncertainty_regions(uncertainty_map, segments, top_k=5):
    """选择不确定性最高的连通超像素区域"""
    # 计算每个超像素的平均不确定性
    segment_uncertainties = []
    unique_segments = np.unique(segments)
    
    for seg_id in unique_segments:
        if seg_id == 0:  # 跳过背景
            continue
        mask = segments == seg_id
        avg_uncertainty = uncertainty_map[mask].mean()
        segment_uncertainties.append((seg_id, avg_uncertainty))
    
    # 按不确定性排序
    segment_uncertainties.sort(key=lambda x: x[1], reverse=True)
    
    # 选择top_k个最不确定的区域
    selected_segments = []
    for i in range(min(top_k, len(segment_uncertainties))):
        seg_id, uncertainty = segment_uncertainties[i]
        selected_segments.append(seg_id)
    
    # 创建选中区域的掩码
    selected_mask = np.zeros_like(segments, dtype=bool)
    for seg_id in selected_segments:
        selected_mask |= (segments == seg_id)
    
    return selected_mask, selected_segments

def ensure_connectivity(mask, min_size=50):
    """确保选中的区域是连通的，移除过小的区域"""
    # 标记连通组件
    labeled_mask = label(mask)
    
    # 获取区域属性
    regions = regionprops(labeled_mask)
    
    # 保留足够大的连通区域
    final_mask = np.zeros_like(mask, dtype=bool)
    for region in regions:
        if region.area >= min_size:
            final_mask[labeled_mask == region.label] = True
    
    return final_mask

def cutmix_operation(image1, image2, mask):
    """执行CutMix操作"""
    result = image1.copy()
    result[mask] = image2[mask]
    return result

def create_paper_figure():
    """创建适合论文的高质量可视化图"""
    # 检查CUDA是否可用
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载模型
    model = BCP_net(in_chns=1, class_num=9)
    model = model.to(device)
    
    # 尝试加载模型权重
    try:
        state = torch.load('model/Synapse/SUMix_fix_u_weight0.5_temperature0.7_2_labeled/self_train/iter_28500_dice_0.6815.pth', 
                          map_location=device)
        model.load_state_dict(state)
        print("Model loaded successfully!")
    except FileNotFoundError:
        print("Model file not found, using random weights for demonstration")
    
    # 加载两个图像
    print("Loading images...")
    
    # 图像1
    h5f1 = h5py.File("code/case0022_slice063.h5", 'r')
    image1 = h5f1['image'][:]
    h5f1.close()
    
    # 图像2
    h5f2 = h5py.File("code/case0028_slice060.h5", 'r')
    image2 = h5f2['image'][:]
    h5f2.close()
    
    # 预处理图像
    def preprocess_image(img):
        x, y = img.shape
        img_resized = zoom(img, (256 / x, 256 / y), order=0)
        img_norm = (img_resized - img_resized.min()) / (img_resized.max() - img_resized.min())
        return img_norm
    
    image1_processed = preprocess_image(image1)
    image2_processed = preprocess_image(image2)
    
    print("Computing uncertainty for image2...")
    
    # 计算图像2的不确定性
    image2_tensor = torch.from_numpy(image2_processed.astype(np.float32)).unsqueeze(0).unsqueeze(0).to(device)
    _, uncertainty2 = monte_carlo_dropout(model, image2_tensor, num_samples=15)
    uncertainty2_np = uncertainty2.squeeze().numpy()
    
    print("Performing SLIC superpixel segmentation...")
    
    # 对两个图像进行SLIC分割
    segments1 = get_slic_superpixels(image1_processed, n_segments=60, compactness=0.1)
    segments2 = get_slic_superpixels(image2_processed, n_segments=60, compactness=0.1)
    
    print("Selecting high uncertainty regions...")
    
    # 选择图像2中不确定性最高的区域
    high_uncertainty_mask, selected_segments = select_high_uncertainty_regions(
        uncertainty2_np, segments2, top_k=5
    )
    
    # 确保连通性
    final_mask = ensure_connectivity(high_uncertainty_mask, min_size=30)
    
    # 执行CutMix
    cutmix_result = cutmix_operation(image1_processed, image2_processed, final_mask)
    
    print("Creating visualization...")
    
    # 创建论文级别的可视化
    fig = plt.figure(figsize=(20, 12))
    
    # 设置整体布局
    gs = fig.add_gridspec(3, 5, height_ratios=[1, 1, 1], width_ratios=[1, 1, 1, 1, 1],
                         hspace=0.3, wspace=0.2)
    
    # 第一行：原始图像和SLIC分割
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.imshow(image1_processed, cmap='gray')
    ax1.set_title('Image 1 (Target)', fontsize=14, fontweight='bold')
    ax1.axis('off')
    
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.imshow(mark_boundaries(image1_processed, segments1, color=(1, 0, 0), mode='thick'))
    ax2.set_title('SLIC Superpixels', fontsize=14, fontweight='bold')
    ax2.axis('off')
    
    ax3 = fig.add_subplot(gs[0, 2])
    ax3.imshow(image2_processed, cmap='gray')
    ax3.set_title('Image 2 (Source)', fontsize=14, fontweight='bold')
    ax3.axis('off')
    
    ax4 = fig.add_subplot(gs[0, 3])
    ax4.imshow(mark_boundaries(image2_processed, segments2, color=(1, 0, 0), mode='thick'))
    ax4.set_title('SLIC Superpixels', fontsize=14, fontweight='bold')
    ax4.axis('off')
    
    ax5 = fig.add_subplot(gs[0, 4])
    im_unc = ax5.imshow(uncertainty2_np, cmap='hot', interpolation='bilinear')
    ax5.set_title('Entropy Uncertainty', fontsize=14, fontweight='bold')
    ax5.axis('off')
    cbar = plt.colorbar(im_unc, ax=ax5, shrink=0.8)
    cbar.set_label('Uncertainty', fontsize=12)
    
    # 第二行：选择过程
    ax6 = fig.add_subplot(gs[1, 0])
    # 显示选中的超像素
    selected_vis = np.zeros_like(segments2)
    for seg_id in selected_segments:
        selected_vis[segments2 == seg_id] = 1
    ax6.imshow(image2_processed, cmap='gray', alpha=0.7)
    ax6.imshow(selected_vis, cmap='Reds', alpha=0.6)
    ax6.set_title('Selected High\nUncertainty Superpixels', fontsize=14, fontweight='bold')
    ax6.axis('off')
    
    ax7 = fig.add_subplot(gs[1, 1])
    ax7.imshow(final_mask, cmap='Reds')
    ax7.set_title('Connected Regions\nMask', fontsize=14, fontweight='bold')
    ax7.axis('off')
    
    ax8 = fig.add_subplot(gs[1, 2])
    # 显示要复制的区域
    copy_region = image2_processed.copy()
    copy_region[~final_mask] = 0
    ax8.imshow(copy_region, cmap='gray')
    ax8.set_title('Regions to Copy', fontsize=14, fontweight='bold')
    ax8.axis('off')
    
    # 添加箭头指示
    ax9 = fig.add_subplot(gs[1, 3])
    ax9.text(0.5, 0.5, '→', fontsize=60, ha='center', va='center', 
             transform=ax9.transAxes, fontweight='bold', color='blue')
    ax9.set_title('CutMix\nOperation', fontsize=14, fontweight='bold')
    ax9.axis('off')
    
    ax10 = fig.add_subplot(gs[1, 4])
    ax10.imshow(cutmix_result, cmap='gray')
    ax10.contour(final_mask, levels=[0.5], colors='red', linewidths=2)
    ax10.set_title('CutMix Result', fontsize=14, fontweight='bold')
    ax10.axis('off')
    
    # 第三行：对比和统计
    ax11 = fig.add_subplot(gs[2, :2])
    # 并排显示原图和结果
    comparison = np.hstack([image1_processed, cutmix_result])
    ax11.imshow(comparison, cmap='gray')
    ax11.axvline(x=256, color='red', linestyle='--', linewidth=2)
    ax11.set_title('Before (Left) vs After (Right) CutMix', fontsize=14, fontweight='bold')
    ax11.axis('off')
    
    # 统计信息
    ax12 = fig.add_subplot(gs[2, 2:])
    stats_text = f"""CutMix Statistics:
    
• Total superpixels in Image 2: {len(np.unique(segments2)) - 1}
• Selected high uncertainty superpixels: {len(selected_segments)}
• Final connected regions: {len(np.unique(label(final_mask))) - 1}
• Copied area: {final_mask.sum()} pixels ({100*final_mask.sum()/final_mask.size:.1f}%)
• Average uncertainty in copied regions: {uncertainty2_np[final_mask].mean():.4f}
• Max uncertainty in copied regions: {uncertainty2_np[final_mask].max():.4f}

Process:
1. Perform SLIC superpixel segmentation on both images
2. Compute entropy-based uncertainty for Image 2
3. Select top-k superpixels with highest uncertainty
4. Ensure spatial connectivity of selected regions
5. Copy selected regions from Image 2 to Image 1"""
    
    ax12.text(0.05, 0.95, stats_text, transform=ax12.transAxes, fontsize=11,
              verticalalignment='top', fontfamily='monospace',
              bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    ax12.axis('off')
    
    plt.suptitle('SLIC-based Uncertainty-guided CutMix for Medical Image Segmentation', 
                 fontsize=18, fontweight='bold', y=0.98)
    
    # 保存高质量图像
    output_path = 'slic_uncertainty_cutmix_paper_figure.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"Paper figure saved to: {output_path}")
    
    # 保存数据
    cutmix_data = {
        'image1_original': image1_processed,
        'image2_original': image2_processed,
        'uncertainty_map': uncertainty2_np,
        'segments1': segments1,
        'segments2': segments2,
        'selected_mask': final_mask,
        'cutmix_result': cutmix_result,
        'selected_segments': selected_segments
    }
    
    np.savez('slic_cutmix_data.npz', **cutmix_data)
    print("CutMix data saved to: slic_cutmix_data.npz")
    
    plt.show()

if __name__ == "__main__":
    create_paper_figure()
